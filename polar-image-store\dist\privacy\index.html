<!DOCTYPE html><html lang="en"> <head><title>Privacy Policy - Polar Image Store</title><meta charset="UTF-8"><link rel="canonical" href="https://infpik.store/privacy/"><meta name="description" content="Beautiful digital images from our collection"><meta name="robots" content="index, follow"><meta property="og:title" content="Privacy Policy - Polar Image Store"><meta property="og:type" content="website"><meta property="og:image" content="http://infpik.store/og-image.jpg"><meta property="og:url" content="https://infpik.store/privacy/"><meta property="og:description" content="Beautiful digital images from our collection"><meta property="og:locale" content="en_US"><meta property="og:site_name" content="Polar Image Store"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@polarimagestore"><meta name="twitter:title" content="Privacy Policy - Polar Image Store"><meta name="twitter:image" content="http://infpik.store/og-image.jpg"><meta name="twitter:image:alt" content="Privacy Policy - Polar Image Store - Polar Image Store"><meta name="twitter:description" content="Beautiful digital images from our collection"><meta name="twitter:creator" content="@polarimagestore"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="sitemap" href="/sitemap-index.xml"><link rel="canonical" href="https://infpik.store/privacy/"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="Astro v5.12.4"><meta name="robots" content="index, follow"><meta name="googlebot" content="index, follow"><meta name="theme-color" content="#6366f1"><meta name="msapplication-TileColor" content="#6366f1"><link rel="stylesheet" href="/_astro/about.BGziO9gN.css"></head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="32" height="32" class="w-8 h-8 text-accent-600">
InfPik
</a> <!-- Mobile Search Bar --> <div class="md:hidden relative flex-1 mx-2"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search products..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div class="hidden md:flex flex-1 max-w-md mx-8"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search products..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12">  <div class="container py-12"> <div class="max-w-4xl mx-auto"> <h1 class="text-4xl font-bold text-primary-900 mb-8">Privacy Policy</h1> <div class="prose prose-lg max-w-none"> <p class="lead text-xl text-primary-700 mb-6">
Last Updated: May 10, 2025
</p> <p>
At Polar Image Store, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or make purchases from our store. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the site.
</p> <h2>Information We Collect</h2> <p>
We collect information that you provide directly to us when you make a purchase through Polar.sh, our payment processor. Please note that we do not offer user accounts or registration on our website. The information collected during checkout may include:
</p> <ul> <li>Personal identifiers (name, email address)</li> <li>Billing information (processed securely by Polar.sh)</li> <li>Purchase details</li> <li>Communications and correspondence with us</li> </ul> <h2>Automatically Collected Information</h2> <p>
When you visit our website, we may automatically collect certain information about your device and usage patterns. This might include:
</p> <ul> <li>Device information (browser type, operating system, device type)</li> <li>Usage data (pages visited, time spent on pages, referring website)</li> <li>IP address and approximate location</li> <li>Cookies and similar tracking technologies</li> </ul> <h2>How We Use Your Information</h2> <p>We may use the information we collect for various purposes, including to:</p> <ul> <li>Process and fulfill your orders through Polar.sh</li> <li>Provide customer support</li> <li>Send transactional emails and order confirmations</li> <li>Improve our website and services</li> <li>Detect and prevent fraud</li> <li>Comply with legal obligations</li> </ul> <h2>Sharing Your Information</h2> <p>We may share your information with:</p> <ul> <li>Polar.sh, our payment processor, to complete transactions</li> <li>Service providers who perform services on our behalf</li> <li>Legal authorities when required by law</li> <li>Business partners in the event of a merger, acquisition, or sale</li> </ul> <h2>Your Rights and Choices</h2> <p>Depending on your location, you may have certain rights regarding your personal information, including:</p> <ul> <li>Access to your personal information</li> <li>Correction of inaccurate or incomplete information</li> <li>Deletion of your personal information</li> <li>Restriction or objection to processing</li> <li>Data portability</li> <li>Withdrawal of consent</li> </ul> <p>
Since we do not maintain user accounts, most of your personal information is processed by Polar.sh during checkout. To exercise your rights regarding transaction data, you may need to contact both us and Polar.sh directly.
</p> <h2>Cookies and Tracking Technologies</h2> <p>
We use cookies and similar tracking technologies to collect information about your browsing activities. You can manage your cookie preferences through your browser settings.
</p> <h2>Data Security</h2> <p>
We implement appropriate technical and organizational measures to protect your personal information. However, no method of transmission over the Internet or electronic storage is 100% secure, so we cannot guarantee absolute security.
</p> <h2>Children's Privacy</h2> <p>
Our services are not directed to individuals under the age of 16. We do not knowingly collect personal information from children under 16. If we become aware that we have collected personal information from a child under 16, we will take steps to delete such information.
</p> <h2>Changes to This Privacy Policy</h2> <p>
We may update this privacy policy from time to time. The updated version will be indicated by an updated "Last Updated" date. We encourage you to review this privacy policy frequently to stay informed about how we are protecting your information.
</p> <h2>Contact Us</h2> <p>
If you have questions or concerns about this privacy policy or our practices, please contact us at:
</p> <p> <a href="mailto:<EMAIL>" class="text-accent-600 hover:text-accent-700"><EMAIL></a> </p> </div> </div> </div>  </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="24" height="24" class="w-6 h-6 text-accent-600">
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p> </div> </div> </footer> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("mobile-menu-button"),c=document.getElementById("mobile-menu");o&&c&&o.addEventListener("click",()=>{c.classList.toggle("hidden")});const i=document.getElementById("productSearch"),d=document.getElementById("mobileProductSearch"),e=document.getElementById("searchResults");let a;async function m(t){const s=t.value.trim();a&&clearTimeout(a),s.length>2?e&&(e.classList.remove("hidden"),e.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching for "${s}"...</span>
              </div>
            </div>
          `,a=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(s)}`)).json();if(e&&!e.classList.contains("hidden"))if(r.results&&r.results.length>0){const u=r.results.map(n=>`
                    <a href="/products/${n.slug}" class="block p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0">
                      <div class="flex items-center gap-3">
                        ${n.image?`<img src="${n.image}" alt="${n.name}" class="w-10 h-10 object-cover rounded-lg">`:""}
                        <div class="flex-1 min-w-0">
                          <div class="text-primary-900 font-medium truncate">${n.name}</div>
                          <div class="text-primary-600 text-sm truncate">${n.description}</div>
                          <div class="text-accent-600 text-sm font-semibold">$${n.price}</div>
                        </div>
                      </div>
                    </a>
                  `).join("");e.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Found ${r.total} result${r.total===1?"":"s"}
                      </div>
                      ${u}
                      <div class="p-3 border-t border-primary-100">
                        <a href="/products?search=${encodeURIComponent(s)}" class="block text-center text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                          View all ${r.total} results →
                        </a>
                      </div>
                    </div>
                  `}else e.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No results found for "${s}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(l){console.error("Search error:",l),e&&!e.classList.contains("hidden")&&(e.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):e&&e.classList.add("hidden")}i&&(i.addEventListener("input",t=>m(t.target)),i.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}})),d&&d.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}}),document.addEventListener("click",t=>{e&&!i?.contains(t.target)&&!e.contains(t.target)&&e.classList.add("hidden")})});</script></body></html>