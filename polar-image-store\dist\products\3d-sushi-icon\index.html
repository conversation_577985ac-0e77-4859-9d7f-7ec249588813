<!DOCTYPE html><html lang="en"> <head><title>3D Sushi Icon - Polar Image Store</title><meta charset="UTF-8"><link rel="canonical" href="http://infpik.store/products/3d-sushi-icon"><meta name="description" content="3D Illustration Sushi Icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more."><meta name="robots" content="index, follow"><meta property="og:title" content="3D Sushi Icon - Polar Image Store"><meta property="og:type" content="product"><meta property="og:image" content="https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/d16336bf-f6c4-4780-9a7a-d2aa54e13fe2/3d%20sushi%20icon.png"><meta property="og:url" content="http://infpik.store/products/3d-sushi-icon"><meta property="og:description" content="3D Illustration Sushi Icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more."><meta property="og:locale" content="en_US"><meta property="og:site_name" content="Polar Image Store"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@polarimagestore"><meta name="twitter:title" content="3D Sushi Icon - Polar Image Store"><meta name="twitter:image" content="https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/d16336bf-f6c4-4780-9a7a-d2aa54e13fe2/3d%20sushi%20icon.png"><meta name="twitter:image:alt" content="3D Sushi Icon - Polar Image Store - Polar Image Store"><meta name="twitter:description" content="3D Illustration Sushi Icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more."><meta name="twitter:creator" content="@polarimagestore"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="sitemap" href="/sitemap-index.xml"><link rel="canonical" href="http://infpik.store/products/3d-sushi-icon"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="Astro v5.12.4"><meta name="robots" content="index, follow"><meta name="googlebot" content="index, follow"><meta name="theme-color" content="#6366f1"><meta name="msapplication-TileColor" content="#6366f1"><link rel="stylesheet" href="/_astro/about.BGziO9gN.css">
<style>@keyframes fade-in{0%{opacity:0}to{opacity:1}}.animate-fade-in[data-astro-cid-gjhjmbi3]{animation:fade-in .3s ease}#lightbox[data-astro-cid-gjhjmbi3].active{display:flex!important;align-items:center;justify-content:center}@media (max-width: 768px){#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-left-20{left:10px;font-size:2rem;padding:.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-right-20{right:10px;font-size:2rem;padding:.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-top-12{top:10px;right:10px;font-size:1.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-bottom-12{bottom:10px}}
</style></head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="32" height="32" class="w-8 h-8 text-accent-600">
InfPik
</a> <!-- Mobile Search Bar --> <div class="md:hidden relative flex-1 mx-2"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search products..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div class="hidden md:flex flex-1 max-w-md mx-8"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search products..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12">   <script type="application/ld+json">{"@context":"https://schema.org","@type":"Product","name":"3D Sushi Icon","description":"3D Illustration Sushi Icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more.","image":["https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/d16336bf-f6c4-4780-9a7a-d2aa54e13fe2/3d%20sushi%20icon.png"],"sku":"afbbee5b-40fe-43e6-b01e-b82a90e5867d","brand":{"@type":"Brand","name":"Polar Image Store"},"offers":{"@type":"Offer","price":1,"priceCurrency":"usd","availability":"https://schema.org/InStock","seller":{"@type":"Organization","name":"Polar Image Store","url":"https://infpik.store"},"url":"http://infpik.store/products/3d-sushi-icon"},"category":"Digital Images","productID":"afbbee5b-40fe-43e6-b01e-b82a90e5867d"}</script> <script type="application/ld+json">{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Home","item":"http://infpik.store"},{"@type":"ListItem","position":2,"name":"Products","item":"http://infpik.store/products"},{"@type":"ListItem","position":3,"name":"3D Sushi Icon","item":"http://infpik.store/products/3d-sushi-icon"}]}</script> <div class="container max-w-7xl"> <div class="mt-8 mb-8"> <nav class="flex items-center gap-2 text-sm text-primary-600"> <a href="/" class="hover:text-accent-600 transition-colors">Home</a> <span>/</span> <a href="/products" class="hover:text-accent-600 transition-colors">Products</a> <span>/</span> <span class="text-primary-900 font-medium">3D Sushi Icon</span> </nav> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12"> <div class="lg:sticky lg:top-8 lg:self-start"> <div class="flex flex-col gap-4" data-astro-cid-gjhjmbi3> <div class="relative aspect-video rounded-2xl overflow-hidden bg-gray-50 cursor-zoom-in flex items-center justify-center group" data-astro-cid-gjhjmbi3> <img src="https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/d16336bf-f6c4-4780-9a7a-d2aa54e13fe2/3d%20sushi%20icon.png" id="mainImage" alt="3D Sushi Icon" loading="eager" fetchpriority="high" onclick="openLightbox(0)" data-astro-cid-gjhjmbi3="true" decoding="async" width="800" height="600" class="max-w-full max-h-full w-auto h-auto object-contain transition-transform duration-300 group-hover:scale-105"> </div>  </div> <!-- Lightbox Modal --> <div id="lightbox" class="hidden fixed inset-0 z-[1000] bg-black/90 animate-fade-in" data-astro-cid-gjhjmbi3> <div class="relative max-w-[90%] max-h-[90%] flex items-center justify-center h-full mx-auto" data-astro-cid-gjhjmbi3> <button class="absolute -top-12 right-0 text-white text-3xl font-bold bg-none border-none cursor-pointer z-[1001] hover:text-gray-300 transition-colors" onclick="closeLightbox()" data-astro-cid-gjhjmbi3>
&times;
</button> <button class="absolute top-1/2 -translate-y-1/2 -left-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="prevImage()" data-astro-cid-gjhjmbi3>
&#8249;
</button> <img src="/placeholder-image.svg" id="lightboxImage" alt="Lightbox image" loading="lazy" data-astro-cid-gjhjmbi3="true" decoding="async" fetchpriority="auto" width="1200" height="800" class="max-w-full max-h-full object-contain rounded-lg"> <button class="absolute top-1/2 -translate-y-1/2 -right-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="nextImage()" data-astro-cid-gjhjmbi3>
&#8250;
</button> <div class="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white bg-black/70 px-4 py-2 rounded-full" data-astro-cid-gjhjmbi3> <span id="imageCounter" data-astro-cid-gjhjmbi3>1 / 1</span> </div> </div> </div>  <script type="module">let o=0,n=[];document.addEventListener("DOMContentLoaded",function(){const e=document.querySelectorAll(".thumbnail img");if(n=Array.from(e).map(t=>t.src),n.length===0){const t=document.getElementById("mainImage");t&&(n=[t.src])}});function c(){const e=document.getElementById("lightbox");e&&(e.classList.remove("active"),document.body.style.overflow="auto")}function g(){n.length>1&&(o=(o-1+n.length)%n.length,i())}function a(){n.length>1&&(o=(o+1)%n.length,i())}function i(){const e=document.getElementById("lightboxImage"),t=document.getElementById("imageCounter");e&&t&&(e.src=n[o],e.alt=`Image ${o+1}`,t.textContent=`${o+1} / ${n.length}`)}document.addEventListener("keydown",function(e){const t=document.getElementById("lightbox");if(t&&t.classList.contains("active"))switch(e.key){case"Escape":c();break;case"ArrowLeft":g();break;case"ArrowRight":a();break}});document.addEventListener("click",function(e){const t=document.getElementById("lightbox");e.target===t&&c()});</script> </div> <div class="space-y-8"> <div> <h1 class="text-3xl lg:text-4xl font-bold text-primary-900 mb-6">3D Sushi Icon</h1> </div> <div class="prose prose-gray max-w-none"> <h3 class="text-lg font-semibold text-primary-900 mb-3">Description</h3> <p class="text-primary-700 leading-relaxed">3D Illustration Sushi Icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more.</p> </div> <div> <h3 class="text-lg font-semibold text-gray-900 mb-3">Tags</h3> <div class="flex flex-wrap gap-2"> <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
#sushi </span> </div> </div> <div class="pt-6 border-t border-primary-200"> <div class="flex gap-3"> <a href="https://buy.polar.sh/polar_cl_vJZ86QNk0Wf15cC4aWsbnPj9NFrYfoKaNQWf81LXzUy" class="flex-1 inline-flex items-center justify-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path> </svg> <span>Buy Now - $1.00</span> </a> <button class="inline-flex items-center justify-center gap-2 bg-primary-50 border-2 border-primary-200 text-primary-700 px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-primary-100 hover:border-primary-300 hover:text-primary-900" onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href)"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path> </svg> <span>Share</span> </button> </div> </div> <div class="bg-primary-50 rounded-2xl p-6"> <h3 class="text-lg font-semibold text-primary-900 mb-4">Product Details</h3> <ul class="space-y-3 text-primary-700"> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Format:</strong> High-resolution digital image</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>License:</strong> Commercial use allowed</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Delivery:</strong> Instant download after purchase</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Support:</strong> Email support included</span> </li> </ul> </div> </div> </div> <div class="mt-16 text-center"> <h2 class="text-3xl font-bold text-primary-900 mb-4">You might also like</h2> <p class="text-primary-600 mb-8">Browse our full collection of digital images</p> <a href="/products" class="inline-flex items-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5">
View All Products
<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path> </svg> </a> </div> </div>  </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="24" height="24" class="w-6 h-6 text-accent-600">
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p> </div> </div> </footer> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("mobile-menu-button"),c=document.getElementById("mobile-menu");o&&c&&o.addEventListener("click",()=>{c.classList.toggle("hidden")});const i=document.getElementById("productSearch"),d=document.getElementById("mobileProductSearch"),e=document.getElementById("searchResults");let a;async function m(t){const s=t.value.trim();a&&clearTimeout(a),s.length>2?e&&(e.classList.remove("hidden"),e.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching for "${s}"...</span>
              </div>
            </div>
          `,a=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(s)}`)).json();if(e&&!e.classList.contains("hidden"))if(r.results&&r.results.length>0){const u=r.results.map(n=>`
                    <a href="/products/${n.slug}" class="block p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0">
                      <div class="flex items-center gap-3">
                        ${n.image?`<img src="${n.image}" alt="${n.name}" class="w-10 h-10 object-cover rounded-lg">`:""}
                        <div class="flex-1 min-w-0">
                          <div class="text-primary-900 font-medium truncate">${n.name}</div>
                          <div class="text-primary-600 text-sm truncate">${n.description}</div>
                          <div class="text-accent-600 text-sm font-semibold">$${n.price}</div>
                        </div>
                      </div>
                    </a>
                  `).join("");e.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Found ${r.total} result${r.total===1?"":"s"}
                      </div>
                      ${u}
                      <div class="p-3 border-t border-primary-100">
                        <a href="/products?search=${encodeURIComponent(s)}" class="block text-center text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                          View all ${r.total} results →
                        </a>
                      </div>
                    </div>
                  `}else e.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No results found for "${s}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(l){console.error("Search error:",l),e&&!e.classList.contains("hidden")&&(e.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):e&&e.classList.add("hidden")}i&&(i.addEventListener("input",t=>m(t.target)),i.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}})),d&&d.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}}),document.addEventListener("click",t=>{e&&!i?.contains(t.target)&&!e.contains(t.target)&&e.classList.add("hidden")})});</script></body></html>