import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct, extractUniqueTags, getTagDisplayName } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

export const GET: APIRoute = async ({ url }) => {
  try {
    const query = url.searchParams.get('q') || 'sushi';
    
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get all products from Polar to extract tags
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Extract all unique tags
    const allTags = extractUniqueTags(products);
    
    // Test search logic step by step
    const searchTerm = query.toLowerCase().trim();
    
    const testResults = allTags.map(tag => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();
      
      const idMatch = tagId.includes(searchTerm);
      const nameMatch = tagName.includes(searchTerm);
      const matches = idMatch || nameMatch;
      
      return {
        originalTag: tag,
        tagId,
        tagName,
        searchTerm,
        idMatch,
        nameMatch,
        matches
      };
    });
    
    const matchingTags = allTags.filter(tag => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();
      return tagId.includes(searchTerm) || tagName.includes(searchTerm);
    });

    // Test tagResults creation
    const tagResults = matchingTags.map(tag => {
      const productCount = products.filter(product =>
        product.tags && product.tags.includes(tag)
      ).length;

      return {
        id: tag,
        name: getTagDisplayName(tag),
        displayName: `#${getTagDisplayName(tag)}`,
        count: productCount,
        url: `/products/tag/${tag}`
      };
    });

    return new Response(
      JSON.stringify({
        query,
        searchTerm,
        allTags,
        matchingTags,
        tagResults,
        testResults,
        totalProducts: products.length
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json'
        } 
      }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        error: 'Test search failed', 
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
