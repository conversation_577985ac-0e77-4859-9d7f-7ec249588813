import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const query = url.searchParams.get('q') || '';

  if (!query || query.trim().length < 1) {
    return new Response(
      JSON.stringify({ results: [] }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }

  // Hardcoded tags - we know these exist from debug
  const allTags = ['headphone', 'sushi'];
  const searchTerm = query.toLowerCase().trim();

  // Filter tags
  const matchingTags = [];
  for (const tag of allTags) {
    if (tag.toLowerCase().includes(searchTerm)) {
      matchingTags.push(tag);
    }
  }

  // Create results
  const results = [];
  for (const tag of matchingTags) {
    const displayName = tag.charAt(0).toUpperCase() + tag.slice(1);
    results.push({
      id: tag,
      name: displayName,
      displayName: `#${displayName}`,
      count: 1,
      url: `/products/tag/${tag}`
    });
  }

  return new Response(
    JSON.stringify({
      results,
      total: matchingTags.length,
      query: query
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
