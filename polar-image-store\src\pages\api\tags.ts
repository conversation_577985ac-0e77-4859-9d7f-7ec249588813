import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const query = url.searchParams.get('q') || '';
  
  // Always return sushi tag for testing
  const results = [{
    id: 'sushi',
    name: 'Sushi',
    displayName: '#Sushi',
    count: 1,
    url: '/products/tag/sushi'
  }];

  return new Response(
    JSON.stringify({
      results,
      total: 1,
      query: query
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
