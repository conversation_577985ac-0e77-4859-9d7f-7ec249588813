import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  try {
    const query = url.searchParams.get('q') || 'sushi';
    
    // Simple hardcoded test
    const mockResults = [
      {
        id: 'sushi',
        name: 'Su<PERSON>',
        displayName: '#Sushi',
        count: 1,
        url: '/products/tag/sushi'
      }
    ];

    return new Response(
      JSON.stringify({
        results: mockResults,
        total: 1,
        query: query
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        error: 'Simple search failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
