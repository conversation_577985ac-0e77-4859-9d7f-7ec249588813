import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const query = url.searchParams.get('q') || '';

  // Debug step by step
  const allTags = ['headphone', 'sushi'];
  const searchTerm = query.toLowerCase().trim();

  const debugInfo = {
    urlHref: url.href,
    urlSearch: url.search,
    searchParamsEntries: Array.from(url.searchParams.entries()),
    originalQuery: query,
    queryType: typeof query,
    searchTerm: searchTerm,
    searchTermLength: searchTerm.length,
    allTags: allTags,
    filterResults: []
  };
  
  // Test filter logic step by step
  for (const tag of allTags) {
    const tagLower = tag.toLowerCase();
    const includes = tagLower.includes(searchTerm);
    
    debugInfo.filterResults.push({
      tag: tag,
      tagLower: tagLower,
      searchTerm: searchTerm,
      includes: includes,
      includesResult: `"${tagLower}".includes("${searchTerm}") = ${includes}`
    });
  }
  
  // Apply actual filter
  const matchingTags = [];
  for (const tag of allTags) {
    if (tag.toLowerCase().includes(searchTerm)) {
      matchingTags.push(tag);
    }
  }
  
  debugInfo['matchingTags'] = matchingTags;

  return new Response(
    JSON.stringify(debugInfo),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
