import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct, extractUniqueTags } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

export const GET: APIRoute = async ({ url }) => {
  try {
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get all products from Polar
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    console.log('Raw product list length:', productList.length);

    // Transform products and debug
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    console.log('Transformed products length:', products.length);

    // Debug each product's tags
    const debugInfo = products.map(product => ({
      id: product.id,
      name: product.name,
      tags: product.tags,
      metadata: productList.find(p => p.id === product.id)?.metadata,
      description: product.description.substring(0, 100)
    }));

    // Extract all unique tags
    const allTags = extractUniqueTags(products);
    console.log('All unique tags:', allTags);

    return new Response(
      JSON.stringify({ 
        totalProducts: products.length,
        allTags,
        debugInfo,
        searchForSushi: allTags.filter(tag => 
          tag.toLowerCase().includes('sushi') || 
          tag.toLowerCase().includes('food') ||
          tag.toLowerCase().includes('japanese')
        )
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json'
        } 
      }
    );

  } catch (error) {
    console.error('Debug API error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Debug failed', 
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
