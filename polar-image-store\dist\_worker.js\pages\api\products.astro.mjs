globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createPolarClient, t as transformPolarProduct } from '../../chunks/polar_6yRTsV5G.mjs';
export { renderers } from '../../renderers.mjs';

const prerender = false;
const GET = async () => {
  try {
    const polar = createPolarClient();
    const organizationId = "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ;
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });
    const productList = response.result?.items || [];
    const products = productList.map(transformPolarProduct);
    return new Response(
      JSON.stringify({ products }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=300"
          // Cache for 5 minutes
        }
      }
    );
  } catch (error) {
    console.error("Error fetching products:", error);
    return new Response(
      JSON.stringify({ error: "Failed to fetch products" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
