import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct, extractUniqueTags, getTagDisplayName } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

export const GET: APIRoute = async ({ url }) => {
  try {
    const query = url.searchParams.get('q');
    
    if (!query || query.trim().length < 1) {
      return new Response(
        JSON.stringify({ results: [] }),
        { 
          status: 200, 
          headers: { 
            'Content-Type': 'application/json',
            'Cache-Control': 'public, max-age=60' // Cache for 1 minute
          } 
        }
      );
    }

    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get all products from Polar to extract tags
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Extract all unique tags
    const allTags = extractUniqueTags(products);
    
    // Filter tags based on search query (partial match, case insensitive)
    const searchTerm = query.toLowerCase().trim();
    const matchingTags = allTags.filter(tag => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();
      
      // Match against both tag ID and display name
      return tagId.includes(searchTerm) || tagName.includes(searchTerm);
    });

    // Count products for each matching tag
    const tagResults = matchingTags.map(tag => {
      const productCount = products.filter(product => 
        product.tags && product.tags.includes(tag)
      ).length;
      
      return {
        id: tag,
        name: getTagDisplayName(tag),
        displayName: `#${getTagDisplayName(tag)}`,
        count: productCount,
        url: `/products/tag/${tag}`
      };
    });

    // Sort by relevance (exact match first, then by product count)
    tagResults.sort((a, b) => {
      const aExactMatch = a.name.toLowerCase() === searchTerm || a.id.toLowerCase() === searchTerm;
      const bExactMatch = b.name.toLowerCase() === searchTerm || b.id.toLowerCase() === searchTerm;
      
      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;
      
      // If both or neither are exact matches, sort by product count (descending)
      if (b.count !== a.count) {
        return b.count - a.count;
      }
      
      // Finally, sort alphabetically
      return a.name.localeCompare(b.name);
    });

    // Limit to top 8 results for dropdown
    const results = tagResults.slice(0, 8);

    return new Response(
      JSON.stringify({ 
        results,
        total: matchingTags.length,
        query: query
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60' // Cache for 1 minute
        } 
      }
    );

  } catch (error) {
    console.error('Tag search API error:', error);
    return new Response(
      JSON.stringify({ error: 'Tag search failed' }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
