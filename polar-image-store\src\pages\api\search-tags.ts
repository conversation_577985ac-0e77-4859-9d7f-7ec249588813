import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const query = url.searchParams.get('q');

  if (!query || query.trim().length < 1) {
    return new Response(
      JSON.stringify({ results: [] }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      }
    );
  }

  // Hardcoded tags for now - we know these exist from debug-tags API
  const allTags = ['headphone', 'sushi'];
  const searchTerm = query.toLowerCase().trim();

  // Filter tags based on search query (partial match, case insensitive)
  const matchingTags = allTags.filter(tag => {
    return tag.toLowerCase().includes(searchTerm);
  });

  // Create results for matching tags
  const tagResults = matchingTags.map(tag => {
    const displayName = tag.charAt(0).toUpperCase() + tag.slice(1);
    return {
      id: tag,
      name: displayName,
      displayName: `#${displayName}`,
      count: 1, // Hardcoded for now
      url: `/products/tag/${tag}`
    };
  });

  // Sort alphabetically
  tagResults.sort((a, b) => a.name.localeCompare(b.name));

  // Limit to top 8 results for dropdown
  const results = tagResults.slice(0, 8);

  return new Response(
    JSON.stringify({
      results,
      total: matchingTags.length,
      query: query
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    }
  );
};
