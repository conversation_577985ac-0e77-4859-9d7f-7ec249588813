globalThis.process ??= {}; globalThis.process.env ??= {};
import { g as decodeKey } from './chunks/astro/server_oCxQtzs6.mjs';
import './chunks/astro-designed-error-pages_A2-zoHVA.mjs';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_DQJXsadJ.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/code/image/polar-image-store/","cacheDir":"file:///D:/code/image/polar-image-store/node_modules/.astro/","outDir":"file:///D:/code/image/polar-image-store/dist/","srcDir":"file:///D:/code/image/polar-image-store/src/","publicDir":"file:///D:/code/image/polar-image-store/public/","buildClientDir":"file:///D:/code/image/polar-image-store/dist/","buildServerDir":"file:///D:/code/image/polar-image-store/dist/_worker.js/","adapterName":"@astrojs/cloudflare","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"about/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"api/debug-tags","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/debug-tags","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/debug-tags\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"debug-tags","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/debug-tags.ts","pathname":"/api/debug-tags","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"api/search-tags","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/search-tags","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/search-tags\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"search-tags","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/search-tags.ts","pathname":"/api/search-tags","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"api/simple-search","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/simple-search","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/simple-search\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"simple-search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/simple-search.ts","pathname":"/api/simple-search","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"api/test-search","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/test-search","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/test-search\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"test-search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/test-search.ts","pathname":"/api/test-search","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"privacy/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/privacy","isIndex":false,"type":"page","pattern":"^\\/privacy\\/?$","segments":[[{"content":"privacy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy.astro","pathname":"/privacy","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"products/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/products","isIndex":true,"type":"page","pattern":"^\\/products\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/products/index.astro","pathname":"/products","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"success/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/success","isIndex":false,"type":"page","pattern":"^\\/success\\/?$","segments":[[{"content":"success","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/success.astro","pathname":"/success","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"terms/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/terms","isIndex":false,"type":"page","pattern":"^\\/terms\\/?$","segments":[[{"content":"terms","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms.astro","pathname":"/terms","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/checkout","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/checkout\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"checkout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/checkout.ts","pathname":"/api/checkout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/products","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/products\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/products.ts","pathname":"/api/products","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/search","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/search\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/search.ts","pathname":"/api/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/webhooks","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/webhooks\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"webhooks","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/webhooks.ts","pathname":"/api/webhooks","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://infpik.store","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/code/image/polar-image-store/src/pages/about.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/privacy.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/[slug].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/category/[category].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/success.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/terms.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000@astro-page:node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint@_@js":"pages/_image.astro.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/api/checkout@_@ts":"pages/api/checkout.astro.mjs","\u0000@astro-page:src/pages/api/debug-tags@_@ts":"pages/api/debug-tags.astro.mjs","\u0000@astro-page:src/pages/api/products@_@ts":"pages/api/products.astro.mjs","\u0000@astro-page:src/pages/api/search@_@ts":"pages/api/search.astro.mjs","\u0000@astro-page:src/pages/api/search-tags@_@ts":"pages/api/search-tags.astro.mjs","\u0000@astro-page:src/pages/api/simple-search@_@ts":"pages/api/simple-search.astro.mjs","\u0000@astro-page:src/pages/api/test-search@_@ts":"pages/api/test-search.astro.mjs","\u0000@astro-page:src/pages/api/webhooks@_@ts":"pages/api/webhooks.astro.mjs","\u0000@astro-page:src/pages/privacy@_@astro":"pages/privacy.astro.mjs","\u0000@astro-page:src/pages/products/category/[category]@_@astro":"pages/products/category/_category_.astro.mjs","\u0000@astro-page:src/pages/products/tag/[tag]@_@astro":"pages/products/tag/_tag_.astro.mjs","\u0000@astro-page:src/pages/products/index@_@astro":"pages/products.astro.mjs","\u0000@astro-page:src/pages/success@_@astro":"pages/success.astro.mjs","\u0000@astro-page:src/pages/terms@_@astro":"pages/terms.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"index.js","\u0000@astro-page:src/pages/products/[slug]@_@astro":"pages/products/_slug_.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","D:/code/image/polar-image-store/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_Cgo0jsQS.mjs","D:/code/image/polar-image-store/node_modules/unstorage/drivers/cloudflare-kv-binding.mjs":"chunks/cloudflare-kv-binding_DMly_2Gl.mjs","\u0000@astrojs-manifest":"manifest_Fl2695IA.mjs","D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts":"_astro/index.astro_astro_type_script_index_0_lang.FvzX8YTC.js","D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts":"_astro/Layout.astro_astro_type_script_index_0_lang.BmTt2Fi3.js","D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts":"_astro/Hero.astro_astro_type_script_index_0_lang.tzVoaVG-.js","D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts":"_astro/ImageGallery.astro_astro_type_script_index_0_lang.dQDmp6mo.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{window.addEventListener(\"categoryChange\",e=>{const l=e.detail.categoryId;r(l)});function r(e){document.querySelectorAll(\".product-item\").forEach(t=>{const s=t.dataset.category,a=t.dataset.featured===\"true\";e===\"all\"||s===e?e===\"all\"&&!a?t.style.display=\"none\":t.style.display=\"block\":t.style.display=\"none\"});const o=document.querySelector(\"#view-all-products-btn\");if(o&&e!==\"all\"){const t=n(e);o.innerHTML=`\n          View All ${t} Products\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n          </svg>\n        `,o.href=`/products/category/${e}`}else o&&e===\"all\"&&(o.innerHTML=`\n          View All Products\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n          </svg>\n        `,o.href=\"/products\")}function n(e){return e.split(\"-\").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(\" \")}});"],["D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const e=document.getElementById(\"categoryScroll\");if(e){let a=!1,s,c;e.addEventListener(\"touchstart\",t=>{a=!0,s=t.touches[0].pageX-e.offsetLeft,c=e.scrollLeft}),e.addEventListener(\"touchend\",()=>{a=!1}),e.addEventListener(\"touchmove\",t=>{if(!a)return;const r=(t.touches[0].pageX-e.offsetLeft-s)*2;e.scrollLeft=c-r}),e.addEventListener(\"mousedown\",t=>{a=!0,s=t.pageX-e.offsetLeft,c=e.scrollLeft,e.style.cursor=\"grabbing\"}),e.addEventListener(\"mouseleave\",()=>{a=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mouseup\",()=>{a=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mousemove\",t=>{if(!a)return;t.preventDefault();const r=(t.pageX-e.offsetLeft-s)*2;e.scrollLeft=c-r}),e.style.cursor=\"grab\";const d=document.querySelectorAll(\".category-tab\");d.forEach(t=>{t.addEventListener(\"click\",o=>{const r=o.currentTarget.dataset.category;d.forEach(i=>{i.classList.remove(\"bg-accent-600\",\"text-white\",\"shadow-md\"),i.classList.add(\"bg-primary-50\",\"text-primary-700\",\"hover:bg-primary-100\",\"hover:text-primary-900\");const l=i.querySelector(\"span\");l&&(l.classList.remove(\"bg-white/20\",\"text-white\"),l.classList.add(\"bg-primary-200\",\"text-primary-600\"))}),t.classList.remove(\"bg-primary-50\",\"text-primary-700\",\"hover:bg-primary-100\",\"hover:text-primary-900\"),t.classList.add(\"bg-accent-600\",\"text-white\",\"shadow-md\");const n=t.querySelector(\"span\");n&&(n.classList.remove(\"bg-primary-200\",\"text-primary-600\"),n.classList.add(\"bg-white/20\",\"text-white\")),window.location.pathname===\"/\"?(console.log(\"📡 Dispatching categoryChange event for homepage:\",r),window.dispatchEvent(new CustomEvent(\"categoryChange\",{detail:{categoryId:r}}))):r===\"all\"?window.location.href=\"/products\":window.location.href=`/products/category/${r}`})}),document.querySelectorAll(\".tag-tab\").forEach(t=>{t.addEventListener(\"click\",o=>{const r=o.currentTarget.dataset.tag;r===\"all\"?window.location.href=\"/products\":window.location.href=`/products/tag/${r}`})})}});"],["D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts","let o=0,n=[];document.addEventListener(\"DOMContentLoaded\",function(){const e=document.querySelectorAll(\".thumbnail img\");if(n=Array.from(e).map(t=>t.src),n.length===0){const t=document.getElementById(\"mainImage\");t&&(n=[t.src])}});function c(){const e=document.getElementById(\"lightbox\");e&&(e.classList.remove(\"active\"),document.body.style.overflow=\"auto\")}function g(){n.length>1&&(o=(o-1+n.length)%n.length,i())}function a(){n.length>1&&(o=(o+1)%n.length,i())}function i(){const e=document.getElementById(\"lightboxImage\"),t=document.getElementById(\"imageCounter\");e&&t&&(e.src=n[o],e.alt=`Image ${o+1}`,t.textContent=`${o+1} / ${n.length}`)}document.addEventListener(\"keydown\",function(e){const t=document.getElementById(\"lightbox\");if(t&&t.classList.contains(\"active\"))switch(e.key){case\"Escape\":c();break;case\"ArrowLeft\":g();break;case\"ArrowRight\":a();break}});document.addEventListener(\"click\",function(e){const t=document.getElementById(\"lightbox\");e.target===t&&c()});"]],"assets":["/_astro/about.BGziO9gN.css","/favicon.svg","/logo.svg","/og-image.jpg","/placeholder-image.svg","/robots.txt","/_astro/Layout.astro_astro_type_script_index_0_lang.BmTt2Fi3.js","/_worker.js/index.js","/_worker.js/renderers.mjs","/_worker.js/<EMAIL>","/_worker.js/_astro-internal_middleware.mjs","/_worker.js/_noop-actions.mjs","/_worker.js/chunks/astro-designed-error-pages_A2-zoHVA.mjs","/_worker.js/chunks/astro_vVsu7Htd.mjs","/_worker.js/chunks/cloudflare-kv-binding_DMly_2Gl.mjs","/_worker.js/chunks/index_C4LTQoxk.mjs","/_worker.js/chunks/index_CDZT1onH.mjs","/_worker.js/chunks/Layout_QFDoOTa8.mjs","/_worker.js/chunks/noop-middleware_DQJXsadJ.mjs","/_worker.js/chunks/path_h5kZAkfu.mjs","/_worker.js/chunks/polar_Cox-3WFU.mjs","/_worker.js/chunks/ProductCard_BqurW8kc.mjs","/_worker.js/chunks/sdk_DEQ9AU5A.mjs","/_worker.js/chunks/server_BIk3FbQ7.mjs","/_worker.js/chunks/sharp_Cgo0jsQS.mjs","/_worker.js/chunks/StructuredData_BhFI0lnD.mjs","/_worker.js/pages/about.astro.mjs","/_worker.js/pages/index.astro.mjs","/_worker.js/pages/privacy.astro.mjs","/_worker.js/pages/products.astro.mjs","/_worker.js/pages/success.astro.mjs","/_worker.js/pages/terms.astro.mjs","/_worker.js/pages/_image.astro.mjs","/_worker.js/_astro/about.BGziO9gN.css","/_worker.js/chunks/astro/server_oCxQtzs6.mjs","/_worker.js/pages/api/checkout.astro.mjs","/_worker.js/pages/api/debug-tags.astro.mjs","/_worker.js/pages/api/products.astro.mjs","/_worker.js/pages/api/search-tags.astro.mjs","/_worker.js/pages/api/search.astro.mjs","/_worker.js/pages/api/simple-search.astro.mjs","/_worker.js/pages/api/test-search.astro.mjs","/_worker.js/pages/api/webhooks.astro.mjs","/_worker.js/pages/products/_slug_.astro.mjs","/_worker.js/pages/products/category/_category_.astro.mjs","/_worker.js/pages/products/tag/_tag_.astro.mjs","/about/index.html","/api/debug-tags","/api/search-tags","/api/simple-search","/api/test-search","/privacy/index.html","/products/index.html","/success/index.html","/terms/index.html","/index.html"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"/E2UPZ5K61NyUUIS8dhChke4uWTgtBbuAA03crh5OME=","sessionConfig":{"driver":"cloudflare-kv-binding","options":{"binding":"SESSION"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/cloudflare-kv-binding_DMly_2Gl.mjs');

export { manifest };
